
import pygame
import random

# Initialize Pygame
pygame.init()

# Game settings
grid_size = 20
grid_width = 600
grid_height = 400
screen = pygame.display.set_mode((grid_width, grid_height))
pygame.display.set_caption("Snake Game")
clock = pygame.time.Clock()

# Colors
black = (0, 0, 0)
green = (0, 255, 0)
red = (255, 0, 0)
white = (255, 255, 255)

# Snake
snake_x = grid_width // 2
snake_y = grid_height // 2
snake_body = []
snake_length = 1
snake_speed = 10
snake_direction = "right"

# Food
food_x = round(random.randrange(0, grid_width - grid_size) / grid_size) * grid_size
food_y = round(random.randrange(0, grid_height - grid_size) / grid_size) * grid_size

def draw_snake(snake_body):
    for segment in snake_body:
        pygame.draw.rect(screen, green, [segment[0], segment[1], grid_size, grid_size])

def display_score(score):
    font = pygame.font.SysFont(None, 25)
    score_surface = font.render("Score: " + str(score), True, white)
    score_rect = score_surface.get_rect()
    screen.blit(score_surface, score_rect)

def game_over():
    font_style = pygame.font.SysFont(None, 50)
    message = font_style.render("Game Over! Press Q-Quit or C-Play Again", True, red)
    message_rect = message.get_rect(center=(grid_width / 2, grid_height / 2))
    screen.blit(message, message_rect)
    pygame.display.update()

    while True:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                pygame.quit()
                quit()
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_q:
                    pygame.quit()
                    quit()
                if event.key == pygame.K_c:
                    game_loop()  # Restart the game


def game_loop():
    global snake_x, snake_y, snake_body, snake_length, snake_direction, food_x, food_y
    snake_x = grid_width // 2
    snake_y = grid_height // 2
    snake_body = []
    snake_length = 1
    snake_direction = "right"
    food_x = round(random.randrange(0, grid_width - grid_size) / grid_size) * grid_size
    food_y = round(random.randrange(0, grid_height - grid_size) / grid_size) * grid_size
    game_over_flag = False
    score = 0

    while not game_over_flag:
        for event in pygame.event.get():
            if event.type == pygame.QUIT:
                game_over_flag = True
            if event.type == pygame.KEYDOWN:
                if event.key == pygame.K_LEFT and snake_direction != "right":
                    snake_direction = "left"
                elif event.key == pygame.K_RIGHT and snake_direction != "left":
                    snake_direction = "right"
                elif event.key == pygame.K_UP and snake_direction != "down":
                    snake_direction = "up"
                elif event.key == pygame.K_DOWN and snake_direction != "up":
                    snake_direction = "down"

        if snake_direction == "right":
            snake_x += grid_size
        elif snake_direction == "left":
            snake_x -= grid_size
        elif snake_direction == "up":
            snake_y -= grid_size
        elif snake_direction == "down":
            snake_y += grid_size

        # Check for game over
        if snake_x >= grid_width or snake_x < 0 or snake_y >= grid_height or snake_y < 0:
            game_over_flag = True
        for segment in snake_body[:-1]:
            if segment[0] == snake_x and segment[1] == snake_y:
                game_over_flag = True

        # Snake body update
        snake_body.append([snake_x, snake_y])
        if len(snake_body) > snake_length:
            del snake_body[0]

        # Food collision
        if snake_x == food_x and snake_y == food_y:
            food_x = round(random.randrange(0, grid_width - grid_size) / grid_size) * grid_size
            food_y = round(random.randrange(0, grid_height - grid_size) / grid_size) * grid_size
            snake_length += 1
            score += 1

        # Drawing
        screen.fill(black)
        draw_snake(snake_body)
        pygame.draw.rect(screen, red, [food_x, food_y, grid_size, grid_size])
        display_score(score)
        pygame.display.update()
        clock.tick(snake_speed)

    game_over()  # Call game_over function after the game loop finishes


game_loop()
pygame.quit()
quit()
