import copy

class Matrix:
    def __init__(self, data):
        self.data = data
        self.rows = len(data)
        self.cols = len(data[0])

    def __str__(self):
        return '\n'.join([str(row) for row in self.data])

    def add(self, other):
        if self.rows != other.rows or self.cols != other.cols:
            raise ValueError("Matrices must have the same dimensions for addition")
        result = []
        for i in range(self.rows):
            row = []
            for j in range(self.cols):
                row.append(self.data[i][j] + other.data[i][j])
            result.append(row)
        return Matrix(result)

    def subtract(self, other):
        if self.rows != other.rows or self.cols != other.cols:
            raise ValueError("Matrices must have the same dimensions for subtraction")
        result = []
        for i in range(self.rows):
            row = []
            for j in range(self.cols):
                row.append(self.data[i][j] - other.data[i][j])
            result.append(row)
        return Matrix(result)

    def multiply(self, other):
        if self.cols != other.rows:
            raise ValueError("Number of columns in the first matrix must be equal to the number of rows in the second matrix")
        result = []
        for i in range(self.rows):
            row = []
            for j in range(other.cols):
                element = 0
                for k in range(self.cols):
                    element += self.data[i][k] * other.data[k][j]
                row.append(element)
            result.append(row)
        return Matrix(result)

    def scalar_multiply(self, scalar):
        result = []
        for i in range(self.rows):
            row = []
            for j in range(self.cols):
                row.append(self.data[i][j] * scalar)
            result.append(row)
        return Matrix(result)

    def transpose(self):
        result = []
        for j in range(self.cols):
            row = []
            for i in range(self.rows):
                row.append(self.data[i][j])
            result.append(row)
        return Matrix(result)

    def determinant(self):
        if self.rows != self.cols:
            raise ValueError("Matrix must be square to calculate determinant")
        if self.rows == 1:
            return self.data[0][0]
        if self.rows == 2:
            return self.data[0][0] * self.data[1][1] - self.data[0][1] * self.data[1][0]
        det = 0
        for j in range(self.cols):
            sub_matrix = []
            for i in range(1, self.rows):
                row = []
                for k in range(self.cols):
                    if k != j:
                        row.append(self.data[i][k])
                sub_matrix.append(row)
            det += ((-1) ** j) * self.data[0][j] * Matrix(sub_matrix).determinant()
        return det

    def inverse(self):
        det = self.determinant()
        if det == 0:
            raise ValueError("Matrix is not invertible")
        if self.rows == 2:
            return Matrix([[self.data[1][1] / det, -self.data[0][1] / det],
                           [-self.data[1][0] / det, self.data[0][0] / det]])
        # For matrices larger than 2x2, use the adjugate matrix method
        adj_matrix = self.adjugate()
        for i in range(self.rows):
            for j in range(self.cols):
                adj_matrix.data[i][j] /= det
        return adj_matrix

    def adjugate(self):
        # The adjugate matrix is the transpose of the cofactor matrix
        cofactor_matrix = self.cofactor()
        return cofactor_matrix.transpose()

    def cofactor(self):
        cofactors = copy.deepcopy(self.data)
        for i in range(self.rows):
            for j in range(self.cols):
                # Create a submatrix by excluding the current row and column
                sub_matrix = []
                for row in range(self.rows):
                    if row != i:
                        new_row = []
                        for col in range(self.cols):
                            if col != j:
                                new_row.append(self.data[row][col])
                        sub_matrix.append(new_row)

                # Calculate the cofactor
                cofactor = ((-1)**(i+j)) * Matrix(sub_matrix).determinant()
                cofactors[i][j] = cofactor

        return Matrix(cofactors)


def get_matrix_input():
    rows = int(input("Enter the number of rows: "))
    cols = int(input("Enter the number of columns: "))
    matrix_data = []
    for i in range(rows):
        row = []
        for j in range(cols):
            element = float(input(f"Enter element at row {i+1}, column {j+1}: "))
            row.append(element)
        matrix_data.append(row)
    return Matrix(matrix_data)

matrix1 = get_matrix_input()
print(matrix1)

matrix2 = get_matrix_input()
print(matrix2)

matrix_sum = matrix1.add(matrix2)
print(matrix_sum)

matrix_diff = matrix1.subtract(matrix2)
print(matrix_diff)

matrix_product = matrix1.multiply(matrix2)
print(matrix_product)

matrix_scalar = matrix1.scalar_multiply(2)
print(matrix_scalar)

matrix_transpose = matrix1.transpose()
print(matrix_transpose)

matrix_determinant = matrix1.determinant()
print(matrix_determinant)

matrix_inverse = matrix1.inverse()
print(matrix_inverse)